<template>
  <div class="aggregation-list">
    <!-- 搜索筛选区域 -->
    <div class="search-filter-section">
      <div class="search-controls">
        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          placeholder="请输入关键字"
          prefix-icon="el-icon-search"
          class="search-input"
          clearable
          @keyup.enter.native="handleSearch"
        />

        <!-- 机组类型下拉选择 -->
        <el-select
          v-model="selectedType"
          placeholder="机组类型"
          class="type-select"
          clearable
          @change="handleTypeChange"
        >
          <el-option label="全部" value=""></el-option>
          <el-option label="调峰机组" value="调峰机组"></el-option>
          <el-option label="基荷机组" value="基荷机组"></el-option>
          <el-option label="调频机组" value="调频机组"></el-option>
        </el-select>
      </div>

      <!-- 新增按钮 -->
      <el-button
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
        class="add-button"
      >
        新增
      </el-button>
    </div>

    <!-- 数据表格 -->
   
      <el-table
        :data="tableData"
        style="width: 100%; height: 600px;"
      
        
      >
        <!-- 序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
          :index="getIndex"
        />

        <!-- 机组名称列 -->
        <el-table-column
          prop="unitName"
          label="机组名称"
         
        />

        <!-- 机组类型列 -->
        <el-table-column
          prop="unitType"
          label="机组类型"
          width="120"
          align="center"
        />

        <!-- 聚合资源数量列 -->
        <el-table-column
          prop="resourceCount"
          label="聚合资源数量"
          width="140"
          align="center"
        />

        <!-- 操作列 -->
        <el-table-column
          label="操作"
          width="200"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDetail(scope.row)"
              class="action-detail"
            >
              详情
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
              class="action-edit"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
              class="action-delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
   

    <!-- 分页区域 -->
    <div class="pagination-section">
      <div class="pagination-info">
        <span>共{{ total }}个</span>
      </div>

      <div class="pagination-controls">
        <!-- 每页条数选择 -->
        <el-select
          v-model="pageSize"
          @change="handleSizeChange"
          class="page-size-select"
        >
          <el-option label="10条/页" :value="10"></el-option>
          <el-option label="20条/页" :value="20"></el-option>
          <el-option label="50条/页" :value="50"></el-option>
          <el-option label="100条/页" :value="100"></el-option>
        </el-select>

        <!-- 分页组件 -->
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="total"
          layout="prev, pager, next, jumper"
          class="pagination"
        />

        <!-- 跳转输入框 -->
        <div class="jump-to">
          <span>跳至</span>
          <el-input
            v-model="jumpPage"
            @keyup.enter.native="handleJumpTo"
            class="jump-input"
            size="small"
          />
          <span>页</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AggregationList",
  data() {
    return {
      // 搜索筛选相关
      searchKeyword: "",
      selectedType: "",

      // 表格相关
      tableData: [],
      loading: false,

      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 1522,
      jumpPage: "",

      // 模拟数据
      mockData: []
    };
  },

  mounted() {
    this.initMockData();
    this.loadTableData();
  },

  methods: {
    // 初始化模拟数据
    initMockData() {
      for (let i = 1; i <= 1522; i++) {
        this.mockData.push({
          id: i,
          unitName: `${i}#调峰机组`,
          unitType: "调峰机组",
          resourceCount: 8
        });
      }
    },

    // 加载表格数据
    loadTableData() {
      this.loading = true;

      // 模拟API请求延迟
      setTimeout(() => {
        let filteredData = [...this.mockData];

        // 根据搜索关键字过滤
        if (this.searchKeyword) {
          filteredData = filteredData.filter(item =>
            item.unitName.includes(this.searchKeyword)
          );
        }

        // 根据机组类型过滤
        if (this.selectedType) {
          filteredData = filteredData.filter(item =>
            item.unitType === this.selectedType
          );
        }

        // 更新总数
        this.total = filteredData.length;

        // 分页处理
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        this.tableData = filteredData.slice(start, end);

        this.loading = false;
      }, 300);
    },

    // 获取序号（考虑分页）
    getIndex(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1;
      this.loadTableData();
    },

    // 机组类型变化处理
    handleTypeChange() {
      this.currentPage = 1;
      this.loadTableData();
    },

    // 新增按钮处理
    handleAdd() {
      this.$message.success("新增功能待实现");
    },

    // 详情按钮处理
    handleDetail(row) {
      this.$message.info(`查看详情：${row.unitName}`);
    },

    // 编辑按钮处理
    handleEdit(row) {
      this.$message.info(`编辑：${row.unitName}`);
    },

    // 删除按钮处理
    handleDelete(row) {
      this.$confirm(`确定要删除 ${row.unitName} 吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$message.success("删除成功");
        this.loadTableData();
      }).catch(() => {
        this.$message.info("已取消删除");
      });
    },

    // 每页条数变化处理
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.loadTableData();
    },

    // 当前页变化处理
    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadTableData();
    },

    // 跳转页面处理
    handleJumpTo() {
      const page = parseInt(this.jumpPage);
      const maxPage = Math.ceil(this.total / this.pageSize);

      if (page && page > 0 && page <= maxPage) {
        this.currentPage = page;
        this.loadTableData();
        this.jumpPage = "";
      } else {
        this.$message.warning(`请输入1-${maxPage}之间的页码`);
      }
    }
  }
};
</script>

<style scoped lang="scss">
.aggregation-list {
    width: 100%;
    height: 400px;
   
    /* position: relative; */
}
/* 搜索筛选区域样式 */
.search-filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  width: 280px;
}

.type-select {
  width: 150px;
}

.add-button {
    @include background_color(ZS);
    /* @indlude background_color(--ZS) */
  /* background-color: var(--ZS);
  border-color: var(--ZS);
  font-weight: 500; */
}

/* .add-button:hover {
  background-color: #5daf34;
  border-color: #5daf34;
} */

/* 表格区域样式 */
.table-section {
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  /* overflow: hidden; */
  
}

/* 操作按钮样式 */
.action-detail,
.action-edit {
  color: #67c23a;
  margin-right: 8px;
}

.action-detail:hover,
.action-edit:hover {
  color: #5daf34;
}

.action-delete {
  color: #f56c6c;
}

.action-delete:hover {
  color: #f45454;
}

/* 分页区域样式 */
.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-info {
  color: #606266;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-size-select {
  width: 100px;
}

.jump-to {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-size: 14px;
}

.jump-input {
  width: 60px;
}

/* Element UI 表格样式覆盖 */
.el-table {
  font-size: 14px;
}

.el-table th {
  background-color: #fafafa;
  color: #303133;
  font-weight: 600;
}

.el-table td {
  padding: 12px 0;
}

.el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: #ebeef5;
}

.el-table--border,
.el-table--group {
  border-color: #ebeef5;
}

.el-table--border td,
.el-table--border th,
.el-table__body-wrapper .el-table--border.is-scrolling-left ~ .el-table__fixed {
  border-right-color: #ebeef5;
}

.el-table--border th,
.el-table__fixed-right-patch {
  border-bottom-color: #ebeef5;
}

/* 分页组件样式覆盖 */
.el-pagination {
  font-weight: normal;
}

.el-pagination .el-pager li {
  font-weight: normal;
}

.el-pagination .el-pager li.active {
  color: #409eff;
  font-weight: 600;
}
</style>